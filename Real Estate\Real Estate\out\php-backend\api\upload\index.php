<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('Method not allowed', 405);
}

try {
    // Check if file was uploaded
    if (!isset($_FILES['file'])) {
        sendError('No file uploaded', 400);
    }
    
    $file = $_FILES['file'];
    
    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        sendError('File upload failed', 400);
    }
    
    // Validate file type
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $file_type = mime_content_type($file['tmp_name']);
    
    if (!in_array($file_type, $allowed_types)) {
        sendError('Invalid file type. Only images are allowed.', 400);
    }
    
    // Validate file size (max 5MB)
    $max_size = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $max_size) {
        sendError('File too large. Maximum size is 5MB.', 400);
    }
    
    // Create uploads directory if it doesn't exist
    $upload_dir = '../../uploads/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Generate unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid('img_', true) . '.' . $file_extension;
    $file_path = $upload_dir . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        // Return the URL to the uploaded file
        $file_url = '/php-backend/uploads/' . $filename;
        
        sendResponse([
            'success' => true,
            'url' => $file_url,
            'filename' => $filename
        ]);
    } else {
        sendError('Failed to save file', 500);
    }
    
} catch (Exception $e) {
    error_log("Upload error: " . $e->getMessage());
    sendError('Upload failed', 500);
}
?>
