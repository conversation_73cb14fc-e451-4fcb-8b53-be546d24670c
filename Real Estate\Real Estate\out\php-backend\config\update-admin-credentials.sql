-- =====================================================
-- UPDATE ADMIN CREDENTIALS SQL
-- For housing.okayy.in Real Estate Website
-- =====================================================

-- This file updates or creates the admin user with correct credentials
-- Email: <EMAIL>
-- Password: Admin@2024!

-- First, try to update existing admin user
UPDATE users 
SET 
    password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    role = 'ADMIN',
    is_active = TRUE,
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- If no rows were affected (admin doesn't exist), insert new admin
INSERT INTO users (id, name, email, password, role, is_active, created_at, updated_at)
SELECT 
    'admin-housing-okayy',
    'Admin User',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'ADMIN',
    TRUE,
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM users WHERE email = '<EMAIL>'
);

-- Verify admin user was created/updated
SELECT 
    id,
    name,
    email,
    role,
    is_active,
    created_at,
    updated_at
FROM users 
WHERE email = '<EMAIL>';

-- Clean up any expired sessions
DELETE FROM user_sessions WHERE expires_at < NOW();

-- Display success message
SELECT 'Admin user setup completed successfully!' as message;
SELECT 'Email: <EMAIL>' as credentials;
SELECT 'Password: Admin@2024!' as password_info;
