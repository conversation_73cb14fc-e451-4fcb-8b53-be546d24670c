<?php
echo "🔧 Creating Admin User...\n\n";

try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Admin credentials
    $adminEmail = '<EMAIL>';
    $adminPassword = 'Admin@2024!';
    $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
    
    // Check if admin exists
    $stmt = $db->prepare('SELECT id FROM User WHERE email = :email');
    $stmt->execute(['email' => $adminEmail]);
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        // Update existing admin
        $stmt = $db->prepare('UPDATE User SET password = :password, role = :role WHERE email = :email');
        $stmt->execute([
            'password' => $hashedPassword,
            'role' => 'ADMIN',
            'email' => $adminEmail
        ]);
        echo "✅ Updated existing admin user\n";
    } else {
        // Create new admin
        $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $adminId = 'admin_' . uniqid();
        $now = date('Y-m-d H:i:s');
        $stmt->execute([
            $adminId,
            'Admin User',
            $adminEmail,
            $hashedPassword,
            'ADMIN',
            1,
            $now,
            $now
        ]);
        echo "✅ Created new admin user\n";
    }
    
    echo "📧 Email: $adminEmail\n";
    echo "🔑 Password: $adminPassword\n";
    echo "\n🎯 You can now login at:\n";
    echo "- Admin Login: http://localhost:3000/admin/login\n";
    echo "- Regular Login: http://localhost:3000/login\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
