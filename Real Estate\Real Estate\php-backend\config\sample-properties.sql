-- Sample approved properties for testing
-- Run this after creating the main database tables and admin user

-- Ensure admin user exists first
INSERT IGNORE INTO users (id, name, email, password, role, is_active, created_at, updated_at) VALUES
('admin-housing-okayy', 'Admin User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ADMIN', TRUE, NOW(), NOW());

-- Insert sample approved properties
INSERT INTO properties (
    id, title, description, price, currency, type, listing_type, 
    bedrooms, bathrooms, area, address, city, state, pincode,
    images, amenities, is_featured, is_approved, approval_status,
    view_count, is_active, owner_id, approved_by, created_at, approved_at
) VALUES 
(
    'prop_sample_001',
    'Luxury 3BHK Apartment in Bandra West',
    'Spacious 3BHK apartment with modern amenities, sea view, and prime location in Bandra West. Perfect for families looking for comfort and convenience.',
    25000000,
    'INR',
    'APARTMENT',
    'SALE',
    3,
    2,
    1200,
    '123 Hill Road, Bandra West',
    'Mumbai',
    'Maharashtra',
    '400050',
    '["https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=1973&auto=format&fit=crop", "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?q=80&w=1973&auto=format&fit=crop"]',
    '["Swimming Pool", "Gym", "Parking", "Security", "Elevator", "Garden"]',
    TRUE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-okayy',
    'admin-housing-okayy',
    NOW(),
    NOW()
),
(
    'prop_sample_002',
    'Modern 2BHK Villa in Gurgaon',
    'Beautiful 2BHK villa with garden, modern kitchen, and excellent connectivity to Delhi NCR. Ideal for small families.',
    8500000,
    'INR',
    'VILLA',
    'SALE',
    2,
    2,
    1500,
    'Sector 45, DLF Phase 2',
    'Gurgaon',
    'Haryana',
    '122002',
    '["https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?q=80&w=1975&auto=format&fit=crop", "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?q=80&w=1953&auto=format&fit=crop"]',
    '["Garden", "Parking", "Security", "Power Backup", "Modular Kitchen"]',
    TRUE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-okayy',
    'admin-housing-okayy',
    NOW(),
    NOW()
),
(
    'prop_sample_003',
    'Affordable 1BHK Flat for Rent',
    'Well-maintained 1BHK flat available for rent in a prime location with all basic amenities.',
    15000,
    'INR',
    'APARTMENT',
    'RENT',
    1,
    1,
    600,
    'Koramangala 4th Block',
    'Bangalore',
    'Karnataka',
    '560034',
    '["https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?q=80&w=1950&auto=format&fit=crop", "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?q=80&w=1980&auto=format&fit=crop"]',
    '["Parking", "Security", "Water Supply", "Internet Ready"]',
    FALSE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-okayy',
    'admin-housing-okayy',
    NOW(),
    NOW()
),
(
    'prop_sample_004',
    'Premium PG for Working Professionals',
    'Fully furnished PG accommodation for working professionals with all modern amenities and excellent connectivity.',
    12000,
    'INR',
    'PG',
    'RENT',
    1,
    1,
    200,
    'HSR Layout, Sector 1',
    'Bangalore',
    'Karnataka',
    '560102',
    '["https://images.unsplash.com/photo-1555854877-bab0e564b8d5?q=80&w=1969&auto=format&fit=crop", "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?q=80&w=1958&auto=format&fit=crop"]',
    '["WiFi", "Laundry", "Meals", "AC", "Security", "Housekeeping"]',
    TRUE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-okayy',
    'admin-housing-okayy',
    NOW(),
    NOW()
),
(
    'prop_sample_005',
    'Spacious 4BHK House in Pune',
    'Large 4BHK independent house with parking, garden, and modern amenities in a peaceful neighborhood.',
    12000000,
    'INR',
    'HOUSE',
    'SALE',
    4,
    3,
    2000,
    'Baner Road, Near IT Park',
    'Pune',
    'Maharashtra',
    '411045',
    '["https://images.unsplash.com/photo-1570129477492-45c003edd2be?q=80&w=1970&auto=format&fit=crop", "https://images.unsplash.com/photo-1605146769289-440113cc3d00?q=80&w=1970&auto=format&fit=crop"]',
    '["Garden", "Parking", "Security", "Power Backup", "Bore Well"]',
    FALSE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-okayy',
    'admin-housing-okayy',
    NOW(),
    NOW()
),
(
    'prop_sample_006',
    'Commercial Office Space in Hyderabad',
    'Prime commercial office space in the heart of Hyderabad IT corridor. Perfect for startups and established businesses.',
    5000000,
    'INR',
    'COMMERCIAL',
    'SALE',
    0,
    2,
    1000,
    'HITEC City, Madhapur',
    'Hyderabad',
    'Telangana',
    '500081',
    '["https://images.unsplash.com/photo-1497366216548-37526070297c?q=80&w=1969&auto=format&fit=crop", "https://images.unsplash.com/photo-1497366811353-6870744d04b2?q=80&w=1969&auto=format&fit=crop"]',
    '["Elevator", "Parking", "Security", "Power Backup", "Conference Room"]',
    TRUE,
    TRUE,
    'APPROVED',
    0,
    TRUE,
    'admin-housing-okayy',
    'admin-housing-okayy',
    NOW(),
    NOW()
);

-- Insert some pending properties for admin testing
INSERT INTO properties (
    id, title, description, price, currency, type, listing_type, 
    bedrooms, bathrooms, area, address, city, state, pincode,
    images, amenities, is_featured, is_approved, approval_status,
    view_count, is_active, owner_id, created_at
) VALUES 
(
    'prop_pending_001',
    'Pending 2BHK Apartment',
    'This property is pending approval from admin.',
    3500000,
    'INR',
    'APARTMENT',
    'SALE',
    2,
    1,
    800,
    'Test Address',
    'Delhi',
    'Delhi',
    '110001',
    '["https://images.unsplash.com/photo-1560518883-ce09059eeffa?q=80&w=1973&auto=format&fit=crop"]',
    '["Parking", "Security"]',
    FALSE,
    FALSE,
    'PENDING',
    0,
    TRUE,
    'admin-housing-okayy',
    NOW()
);
