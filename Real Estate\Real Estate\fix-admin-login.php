<?php
echo "🔧 Fixing Admin Login Issues...\n\n";

// Fix 1: Create admin user in SQLite database (for development)
echo "1. Setting up SQLite database (development)...\n";
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if User table exists
    $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='User'")->fetchAll();
    if (empty($tables)) {
        echo "   ❌ User table doesn't exist. Need to run Prisma migrations first.\n";
        echo "   Run: npx prisma db push\n";
    } else {
        echo "   ✅ User table exists\n";
        
        // Create admin user
        $adminEmail = '<EMAIL>';
        $adminPassword = 'Admin@2024!';
        $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
        
        // Check if admin exists
        $stmt = $db->prepare('SELECT id FROM User WHERE email = :email');
        $stmt->execute(['email' => $adminEmail]);
        $existingAdmin = $stmt->fetch();
        
        if ($existingAdmin) {
            // Update existing admin
            $stmt = $db->prepare('UPDATE User SET password = :password, role = :role WHERE email = :email');
            $stmt->execute([
                'password' => $hashedPassword,
                'role' => 'ADMIN',
                'email' => $adminEmail
            ]);
            echo "   ✅ Updated existing admin user\n";
        } else {
            // Create new admin
            $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
            $adminId = 'admin_' . uniqid();
            $now = date('Y-m-d H:i:s');
            $stmt->execute([
                $adminId,
                'Admin User',
                $adminEmail,
                $hashedPassword,
                'ADMIN',
                1,
                $now,
                $now
            ]);
            echo "   ✅ Created new admin user\n";
        }
        
        echo "   📧 Email: $adminEmail\n";
        echo "   🔑 Password: $adminPassword\n";
    }
} catch (Exception $e) {
    echo "   ❌ SQLite Error: " . $e->getMessage() . "\n";
}

echo "\n2. Checking MySQL database configuration...\n";

// Fix 2: Check MySQL database configuration
try {
    require_once 'php-backend/config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    echo "   ✅ MySQL connection successful\n";
    
    // Check if users table exists
    $stmt = $db->query("SHOW TABLES LIKE 'users'");
    $table = $stmt->fetch();
    
    if (!$table) {
        echo "   ❌ Users table doesn't exist in MySQL\n";
        echo "   📝 Need to import database.sql\n";
    } else {
        echo "   ✅ Users table exists in MySQL\n";
        
        // Check for admin user
        $stmt = $db->prepare('SELECT id, email FROM users WHERE role = "ADMIN"');
        $stmt->execute();
        $admins = $stmt->fetchAll();
        
        if (empty($admins)) {
            echo "   ❌ No admin users found in MySQL\n";
            
            // Create admin user in MySQL
            $adminEmail = '<EMAIL>';
            $adminPassword = 'Admin@2024!';
            $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
            $adminId = 'admin_' . uniqid();
            
            $stmt = $db->prepare('INSERT INTO users (id, name, email, password, role, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())');
            $stmt->execute([
                $adminId,
                'Admin User',
                $adminEmail,
                $hashedPassword,
                'ADMIN',
                1
            ]);
            echo "   ✅ Created admin user in MySQL\n";
            echo "   📧 Email: $adminEmail\n";
            echo "   🔑 Password: $adminPassword\n";
        } else {
            echo "   ✅ Admin users found in MySQL:\n";
            foreach ($admins as $admin) {
                echo "      - " . $admin['email'] . "\n";
            }
        }
    }
} catch (Exception $e) {
    echo "   ❌ MySQL Error: " . $e->getMessage() . "\n";
    if (strpos($e->getMessage(), 'your_secure_password') !== false) {
        echo "   🔧 Fix: Update database password in php-backend/config/database.php\n";
        echo "   Replace 'your_secure_password' with your actual database password\n";
    }
}

echo "\n3. Checking authentication endpoints...\n";

// Fix 3: Test authentication endpoints
$endpoints = [
    'php-backend/api/auth/login.php' => 'Login API',
    'php-backend/api/auth/check-session.php' => 'Session Check API'
];

foreach ($endpoints as $file => $name) {
    if (file_exists($file)) {
        echo "   ✅ $name exists\n";
    } else {
        echo "   ❌ $name missing\n";
    }
}

echo "\n🎯 Summary:\n";
echo "1. For development (localhost): Use SQLite <NAME_EMAIL>\n";
echo "2. For production: Update MySQL password in php-backend/config/database.php\n";
echo "3. Admin credentials: <EMAIL> / Admin@2024!\n";
echo "4. Test login at: http://localhost:3000/admin/login\n";

echo "\n✨ Admin login should now work!\n";
?>
