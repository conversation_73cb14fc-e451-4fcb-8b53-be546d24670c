<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if user is admin
    $currentUser = getCurrentUser($db);
    if (!$currentUser || $currentUser['role'] !== 'ADMIN') {
        sendError('Admin access required', 403);
    }
    
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['user_id']) || !isset($input['is_active'])) {
        sendError('Missing required fields', 400);
    }
    
    $userId = $input['user_id'];
    $isActive = $input['is_active'] ? 1 : 0;
    
    // Check if user exists and is not an admin
    $checkQuery = "SELECT role FROM users WHERE id = :user_id";
    $checkStmt = $db->prepare($checkQuery);
    $checkStmt->bindParam(':user_id', $userId);
    $checkStmt->execute();
    
    $user = $checkStmt->fetch();
    if (!$user) {
        sendError('User not found', 404);
    }
    
    if ($user['role'] === 'ADMIN') {
        sendError('Cannot modify admin user status', 403);
    }
    
    // Update user status
    $query = "UPDATE users SET is_active = :is_active WHERE id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':is_active', $isActive);
    $stmt->bindParam(':user_id', $userId);
    
    if ($stmt->execute()) {
        sendResponse([
            'success' => true,
            'message' => 'User status updated successfully'
        ]);
    } else {
        sendError('Failed to update user status', 500);
    }
    
} catch (Exception $e) {
    error_log("Toggle user status error: " . $e->getMessage());
    sendError('Failed to update user status', 500);
}

// Helper function to get current user
function getCurrentUser($db) {
    // Check for session token in cookies
    if (!isset($_COOKIE['session_token'])) {
        return null;
    }
    
    $session_token = $_COOKIE['session_token'];
    
    $query = "SELECT u.* FROM users u 
              JOIN user_sessions s ON u.id = s.user_id 
              WHERE s.session_token = :token AND s.expires_at > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();
    
    return $stmt->fetch();
}
?>
