// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(USER)
  phone         String?
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts           Account[]
  sessions           Session[]
  properties         Property[]
  inquiries          Inquiry[]
  savedProperties    SavedProperty[]
  blogPosts          BlogPost[]
  approvedProperties Property[]      @relation("ApprovedBy")
}

model Property {
  id                 String              @id @default(cuid())
  title              String
  description        String?
  price              Int
  currency           String              @default("INR")
  type               PropertyType
  listingType        ListingType         @default(SALE)
  accommodationType  AccommodationType?
  pgRoomType         PGRoomType?
  pgGenderPreference PGGenderPreference?
  status             PropertyStatus      @default(AVAILABLE)
  bedrooms           Int?
  bathrooms          Int?
  area               Int?
  address            String
  city               String
  state              String
  pincode            String
  latitude           Float?
  longitude          Float?
  images             String? // JSON string
  amenities          String? // JSON string
  isFeatured         Boolean             @default(false)
  isApproved         Boolean             @default(false)
  approvalStatus     ApprovalStatus      @default(PENDING)
  rejectionReason    String?
  adminNotes         String?
  viewCount          Int                 @default(0)
  isActive           Boolean             @default(true)
  ownerId            String
  approvedBy         String?
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  approvedAt         DateTime?

  owner           User            @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  approver        User?           @relation("ApprovedBy", fields: [approvedBy], references: [id])
  inquiries       Inquiry[]
  savedProperties SavedProperty[]
}

model SavedProperty {
  id         String   @id @default(cuid())
  userId     String
  propertyId String
  createdAt  DateTime @default(now())

  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@unique([userId, propertyId])
}

model Inquiry {
  id         String        @id @default(cuid())
  propertyId String
  userId     String
  name       String
  email      String
  phone      String?
  message    String?
  status     InquiryStatus @default(NEW)
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt

  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model ContactMessage {
  id        String             @id @default(cuid())
  name      String
  email     String
  phone     String?
  message   String
  type      ContactMessageType @default(GENERAL)
  status    ContactStatus      @default(NEW)
  createdAt DateTime           @default(now())
}

model BlogPost {
  id            String   @id @default(cuid())
  title         String
  slug          String   @unique
  content       String
  excerpt       String?
  featuredImage String?
  published     Boolean  @default(false)
  tags          String? // JSON string
  category      String?
  authorId      String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  author User @relation(fields: [authorId], references: [id], onDelete: Cascade)
}

model UserSession {
  id           String   @id @default(cuid())
  userId       String
  sessionToken String   @unique
  expiresAt    DateTime
  createdAt    DateTime @default(now())
}

enum UserRole {
  USER
  ADMIN
}

enum PropertyType {
  APARTMENT
  HOUSE
  VILLA
  PLOT
  COMMERCIAL
  OFFICE
  PG
}

enum ListingType {
  RENT
  SALE
}

enum AccommodationType {
  FULL_HOUSE
  FLAT
  ONE_BHK
  TWO_BHK
  THREE_BHK
  FOUR_BHK
}

enum PGRoomType {
  SINGLE
  DOUBLE
  TRIPLE
  FOUR_SHARING
  DORMITORY
}

enum PGGenderPreference {
  MALE
  FEMALE
  MIXED
}

enum PropertyStatus {
  AVAILABLE
  SOLD
  RENTED
  PENDING
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

enum InquiryStatus {
  NEW
  CONTACTED
  QUALIFIED
  CLOSED
}

enum ContactMessageType {
  GENERAL
  VALUATION
  INQUIRY
}

enum ContactStatus {
  NEW
  READ
  REPLIED
}
