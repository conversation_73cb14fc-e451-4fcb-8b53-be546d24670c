'use client'

import { useState } from 'react'
import { authAPI } from '@/config/api'

export default function TestAPIPage() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testLogin = async () => {
    setLoading(true)
    setResult('Testing...')
    
    try {
      console.log('🔍 Starting API test...')
      const response = await authAPI.login('<EMAIL>', 'Admin@2024!')
      console.log('🔍 API test response:', response)
      
      setResult(`✅ SUCCESS: ${JSON.stringify(response, null, 2)}`)
    } catch (error: any) {
      console.error('🔍 API test error:', error)
      setResult(`❌ ERROR: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const testDirectFetch = async () => {
    setLoading(true)
    setResult('Testing direct fetch...')
    
    try {
      console.log('🔍 Starting direct fetch test...')
      const response = await fetch('http://localhost:8000/php-backend/api/auth/login.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'Admin@2024!'
        })
      })
      
      console.log('🔍 Direct fetch response:', response)
      const data = await response.json()
      console.log('🔍 Direct fetch data:', data)
      
      setResult(`✅ DIRECT FETCH SUCCESS: ${JSON.stringify(data, null, 2)}`)
    } catch (error: any) {
      console.error('🔍 Direct fetch error:', error)
      setResult(`❌ DIRECT FETCH ERROR: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">🔧 API Test Page</h1>
      
      <div className="space-y-4">
        <button 
          onClick={testLogin}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test authAPI.login()'}
        </button>
        
        <button 
          onClick={testDirectFetch}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50 ml-4"
        >
          {loading ? 'Testing...' : 'Test Direct Fetch'}
        </button>
      </div>
      
      <div className="mt-8">
        <h2 className="text-lg font-semibold mb-2">Result:</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
          {result || 'Click a button to test...'}
        </pre>
      </div>
      
      <div className="mt-4 text-sm text-gray-600">
        <p>Open browser console (F12) to see detailed logs</p>
      </div>
    </div>
  )
}
