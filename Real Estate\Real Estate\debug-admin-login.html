<!DOCTYPE html>
<html>
<head>
    <title>Debug Admin Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        input { padding: 8px; width: 300px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>🔧 Debug Admin Login</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label>Email:</label><br>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label>Password:</label><br>
            <input type="password" id="password" value="Admin@2024!" required>
        </div>
        <div class="form-group">
            <button type="submit">🔑 Test Admin Login</button>
        </div>
    </form>
    
    <div id="result"></div>
    <div id="debug"></div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            const debugDiv = document.getElementById('debug');
            
            resultDiv.innerHTML = '⏳ Testing login...';
            debugDiv.innerHTML = '';
            
            try {
                console.log('🔍 Starting login test...');
                console.log('Email:', email);
                console.log('Password length:', password.length);
                
                const url = 'http://localhost:8000/php-backend/api/auth/login.php';
                console.log('API URL:', url);
                
                const requestData = { email, password };
                console.log('Request data:', requestData);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify(requestData)
                });
                
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);
                console.log('Response headers:', [...response.headers.entries()]);
                
                const responseText = await response.text();
                console.log('Raw response:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                    console.log('Parsed response:', data);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    throw new Error('Invalid JSON response: ' + responseText);
                }
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Login Successful!</h3>
                            <p><strong>User:</strong> ${data.user.email}</p>
                            <p><strong>Role:</strong> ${data.user.role}</p>
                            <p><strong>Name:</strong> ${data.user.name}</p>
                            <p><strong>Active:</strong> ${data.user.is_active ? 'Yes' : 'No'}</p>
                            <p><strong>Session Token:</strong> ${data.session_token.substring(0, 30)}...</p>
                            <p><strong>Expires:</strong> ${data.expires_at}</p>
                        </div>
                    `;
                    
                    // Test session storage
                    localStorage.setItem('admin_session', data.session_token);
                    console.log('Session token stored in localStorage');
                    
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Login Failed</h3>
                            <p><strong>Error:</strong> ${data.error || 'Unknown error'}</p>
                            <p><strong>Status:</strong> ${response.status}</p>
                        </div>
                    `;
                }
                
                debugDiv.innerHTML = `
                    <h4>🔍 Debug Info:</h4>
                    <pre>${JSON.stringify({
                        url: url,
                        status: response.status,
                        ok: response.ok,
                        headers: Object.fromEntries(response.headers.entries()),
                        data: data
                    }, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('Login error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network/Request Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Check browser console for details</p>
                    </div>
                `;
                
                debugDiv.innerHTML = `
                    <h4>🔍 Error Debug:</h4>
                    <pre>${error.stack}</pre>
                `;
            }
        });
        
        // Test if we can reach the API at all
        window.addEventListener('load', async function() {
            try {
                const response = await fetch('http://localhost:8000/php-backend/api/auth/login.php', {
                    method: 'OPTIONS'
                });
                console.log('OPTIONS request status:', response.status);
            } catch (error) {
                console.error('OPTIONS request failed:', error);
            }
        });
    </script>
</body>
</html>
