<?php
echo "🧪 Testing Admin Login Systems...\n\n";

// Test 1: SQLite Database (Development)
echo "1. Testing SQLite Database (Development):\n";
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $db->prepare('SELECT id, name, email, role FROM User WHERE role = "ADMIN"');
    $stmt->execute();
    $admins = $stmt->fetchAll();
    
    if (empty($admins)) {
        echo "   ❌ No admin users found\n";
    } else {
        echo "   ✅ Admin users found:\n";
        foreach ($admins as $admin) {
            echo "      - " . $admin['email'] . " (" . $admin['name'] . ")\n";
        }
    }
} catch (Exception $e) {
    echo "   ❌ SQLite Error: " . $e->getMessage() . "\n";
}

// Test 2: MySQL Database (Production)
echo "\n2. Testing MySQL Database (Production):\n";
try {
    // Check if we can connect to MySQL
    $host = 'localhost';
    $db_name = 'u357173570_housingokayy';
    $username = 'u357173570_housingokayy';
    $password = 'your_secure_password'; // This is the placeholder password
    
    $db = new PDO("mysql:host=$host;dbname=$db_name", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "   ✅ MySQL connection successful\n";
    
    // Check for users table
    $stmt = $db->query("SHOW TABLES LIKE 'users'");
    $table = $stmt->fetch();
    
    if (!$table) {
        echo "   ❌ Users table doesn't exist\n";
    } else {
        echo "   ✅ Users table exists\n";
        
        $stmt = $db->prepare('SELECT id, email FROM users WHERE role = "ADMIN"');
        $stmt->execute();
        $admins = $stmt->fetchAll();
        
        if (empty($admins)) {
            echo "   ❌ No admin users found\n";
        } else {
            echo "   ✅ Admin users found:\n";
            foreach ($admins as $admin) {
                echo "      - " . $admin['email'] . "\n";
            }
        }
    }
} catch (Exception $e) {
    echo "   ❌ MySQL Error: " . $e->getMessage() . "\n";
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "   🔧 Fix: Update the database password in php-backend/config/database.php\n";
        echo "   Replace 'your_secure_password' with your actual MySQL password\n";
    }
}

echo "\n3. Testing Admin Login Endpoints:\n";

// Test 3: Check if admin login files exist
$files = [
    'src/app/admin/login/page.tsx' => 'Next.js Admin Login Page',
    'php-admin/login.php' => 'PHP Admin Login (Legacy)',
    'php-backend/api/auth/login.php' => 'API Login Endpoint',
    'php-backend/api/auth/check-session.php' => 'Session Check Endpoint'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ $description exists\n";
    } else {
        echo "   ❌ $description missing\n";
    }
}

echo "\n🎯 Summary:\n";
echo "For development (localhost:3000):\n";
echo "- Use: http://localhost:3000/admin/login\n";
echo "- Database: SQLite (prisma/dev.db)\n";
echo "- Credentials: <EMAIL> / Admin@2024!\n";

echo "\nFor production deployment:\n";
echo "- Update MySQL password in php-backend/config/database.php\n";
echo "- Import database.sql to create tables and admin user\n";
echo "- Use the same credentials: <EMAIL> / Admin@2024!\n";

echo "\n✨ Admin login should now work for development!\n";
?>
