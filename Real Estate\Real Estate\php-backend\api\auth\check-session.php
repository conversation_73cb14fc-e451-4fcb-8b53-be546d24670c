<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get session token from cookie or header
    $session_token = null;
    
    if (isset($_COOKIE['session_token'])) {
        $session_token = $_COOKIE['session_token'];
    } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $auth_header = $_SERVER['HTTP_AUTHORIZATION'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $session_token = $matches[1];
        }
    }
    
    if (!$session_token) {
        sendResponse(['user' => null]);
    }
    
    // Check session validity - handle both SQLite and MySQL table names
    $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;

    if ($isSQLite) {
        // SQLite database (development)
        $query = "SELECT s.userId as user_id, s.expiresAt as expires_at, u.id, u.name, u.email, u.role, u.isActive as is_active
                  FROM UserSession s
                  JOIN User u ON s.userId = u.id
                  WHERE s.sessionToken = :token AND s.expiresAt > datetime('now')";
        $cleanup_query = "DELETE FROM UserSession WHERE sessionToken = :token";
    } else {
        // MySQL database (production)
        $query = "SELECT s.user_id, s.expires_at, u.id, u.name, u.email, u.role, u.is_active
                  FROM user_sessions s
                  JOIN users u ON s.user_id = u.id
                  WHERE s.session_token = :token AND s.expires_at > NOW()";
        $cleanup_query = "DELETE FROM user_sessions WHERE session_token = :token";
    }

    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();

    $session = $stmt->fetch();

    if (!$session) {
        // Clean up expired session
        $cleanup_stmt = $db->prepare($cleanup_query);
        $cleanup_stmt->bindParam(':token', $session_token);
        $cleanup_stmt->execute();

        sendResponse(['user' => null]);
    }
    
    if (!$session['is_active']) {
        sendResponse(['user' => null]);
    }
    
    // Return user data
    $user = [
        'id' => $session['id'],
        'name' => $session['name'],
        'email' => $session['email'],
        'role' => $session['role']
    ];
    
    sendResponse(['user' => $user]);
    
} catch (Exception $e) {
    error_log("Session check error: " . $e->getMessage());
    sendResponse(['user' => null]);
}
?>
