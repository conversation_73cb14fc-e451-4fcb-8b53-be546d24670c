<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $input = json_decode(file_get_contents('php://input'), true);
    $input = validateInput($input, ['email', 'password']);
    
    $email = sanitizeInput($input['email']);
    $password = $input['password'];
    
    // Find user by email - handle both SQLite (User) and MySQL (users) table names
    $isSQLite = strpos($db->getAttribute(PDO::ATTR_DRIVER_NAME), 'sqlite') !== false;

    if ($isSQLite) {
        // SQLite database (development)
        $query = "SELECT id, name, email, password, role, isActive as is_active FROM User WHERE email = :email";
    } else {
        // MySQL database (production)
        $query = "SELECT id, name, email, password, role, is_active FROM users WHERE email = :email";
    }

    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();

    $user = $stmt->fetch();

    if (!$user) {
        sendError('Invalid email or password', 401);
    }

    if (!$user['is_active']) {
        sendError('Account is deactivated', 401);
    }
    
    // Verify password
    if (!password_verify($password, $user['password'])) {
        sendError('Invalid email or password', 401);
    }
    
    // Generate session token
    $session_token = bin2hex(random_bytes(32));
    $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
    
    // Save session - handle both SQLite and MySQL table names
    if ($isSQLite) {
        // SQLite database (development) - use UserSession table
        $session_query = "INSERT INTO UserSession (id, userId, sessionToken, expiresAt) VALUES (:id, :user_id, :token, :expires)";
    } else {
        // MySQL database (production) - use user_sessions table
        $session_query = "INSERT INTO user_sessions (id, user_id, session_token, expires_at) VALUES (:id, :user_id, :token, :expires)";
    }

    $session_stmt = $db->prepare($session_query);
    $session_id = uniqid('sess_', true);
    $session_stmt->bindParam(':id', $session_id);
    $session_stmt->bindParam(':user_id', $user['id']);
    $session_stmt->bindParam(':token', $session_token);
    $session_stmt->bindParam(':expires', $expires_at);
    $session_stmt->execute();
    
    // Set session cookie
    setcookie('session_token', $session_token, strtotime('+30 days'), '/', '', false, true);
    
    // Return user data (without password)
    unset($user['password']);
    
    sendResponse([
        'success' => true,
        'user' => $user,
        'session_token' => $session_token,
        'expires_at' => $expires_at
    ]);
    
} catch (Exception $e) {
    error_log("Login error: " . $e->getMessage());
    sendError('Login failed', 500);
}
?>
