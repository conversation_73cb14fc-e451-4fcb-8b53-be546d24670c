<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get query parameters
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? min(50, max(1, intval($_GET['limit']))) : 12;
    $offset = ($page - 1) * $limit;
    
    // Build WHERE conditions
    $where_conditions = ["p.approval_status = 'APPROVED'"]; // Only show approved properties
    $params = [];
    
    // Property type filter
    if (isset($_GET['type']) && !empty($_GET['type'])) {
        $where_conditions[] = "p.type = :type";
        $params[':type'] = $_GET['type'];
    }
    
    // Listing type filter (RENT/SALE)
    if (isset($_GET['listingType']) && !empty($_GET['listingType'])) {
        $where_conditions[] = "p.listing_type = :listing_type";
        $params[':listing_type'] = $_GET['listingType'];
    }
    
    // Price range filters
    if (isset($_GET['minPrice']) && !empty($_GET['minPrice'])) {
        $where_conditions[] = "p.price >= :min_price";
        $params[':min_price'] = floatval($_GET['minPrice']);
    }
    
    if (isset($_GET['maxPrice']) && !empty($_GET['maxPrice'])) {
        $where_conditions[] = "p.price <= :max_price";
        $params[':max_price'] = floatval($_GET['maxPrice']);
    }
    
    // Bedrooms filter
    if (isset($_GET['bedrooms']) && !empty($_GET['bedrooms'])) {
        $where_conditions[] = "p.bedrooms >= :bedrooms";
        $params[':bedrooms'] = intval($_GET['bedrooms']);
    }
    
    // City filter
    if (isset($_GET['city']) && !empty($_GET['city'])) {
        $where_conditions[] = "p.city LIKE :city";
        $params[':city'] = '%' . $_GET['city'] . '%';
    }
    
    // Search query
    if (isset($_GET['q']) && !empty($_GET['q'])) {
        $search = '%' . $_GET['q'] . '%';
        $where_conditions[] = "(p.title LIKE :search OR p.description LIKE :search2 OR p.city LIKE :search3 OR p.address LIKE :search4)";
        $params[':search'] = $search;
        $params[':search2'] = $search;
        $params[':search3'] = $search;
        $params[':search4'] = $search;
    }
    
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total FROM properties p $where_clause";
    $count_stmt = $db->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    $count_stmt->execute();
    $total_count = $count_stmt->fetch()['total'];
    
    // Get properties with owner information
    $query = "SELECT p.*, u.name as owner_name, u.email as owner_email, u.phone as owner_phone
              FROM properties p 
              JOIN users u ON p.owner_id = u.id 
              $where_clause
              ORDER BY p.created_at DESC 
              LIMIT :limit OFFSET :offset";
    
    $stmt = $db->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    
    $properties = $stmt->fetchAll();
    
    // Process properties data
    foreach ($properties as &$property) {
        // Parse JSON fields safely
        $property['images'] = json_decode($property['images'], true) ?: [];
        $property['amenities'] = json_decode($property['amenities'], true) ?: [];
        
        // Add owner information
        $property['owner'] = [
            'name' => $property['owner_name'],
            'email' => $property['owner_email'],
            'phone' => $property['owner_phone']
        ];
        
        // Clean up duplicate fields
        unset($property['owner_name'], $property['owner_email'], $property['owner_phone']);
        
        // Ensure numeric fields are properly typed
        $property['price'] = floatval($property['price']);
        $property['area'] = floatval($property['area']);
        $property['bedrooms'] = $property['bedrooms'] ? intval($property['bedrooms']) : null;
        $property['bathrooms'] = $property['bathrooms'] ? intval($property['bathrooms']) : null;
        
        // Add formatted dates
        $property['createdAt'] = $property['created_at'];
    }
    
    // Calculate pagination
    $total_pages = ceil($total_count / $limit);
    $has_next = $page < $total_pages;
    $has_prev = $page > 1;
    
    sendResponse([
        'success' => true,
        'properties' => $properties,
        'pagination' => [
            'currentPage' => $page,
            'totalPages' => $total_pages,
            'totalItems' => intval($total_count),
            'hasNext' => $has_next,
            'hasPrev' => $has_prev,
            'limit' => $limit
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Properties search error: " . $e->getMessage());
    sendError('Failed to fetch properties', 500);
}
?>
