<?php
try {
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "SQLite database connection successful\n";
    
    // List all tables
    $tables = $db->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll();
    echo "Tables in database:\n";
    foreach ($tables as $table) {
        echo "- " . $table['name'] . "\n";
    }
    
    // Check User table structure if it exists
    $userTable = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='User'")->fetchAll();
    if (!empty($userTable)) {
        echo "\nUser table structure:\n";
        $columns = $db->query("PRAGMA table_info(User)")->fetchAll();
        foreach ($columns as $column) {
            echo "- " . $column['name'] . " (" . $column['type'] . ")\n";
        }
        
        // Count users
        $userCount = $db->query('SELECT COUNT(*) as count FROM User')->fetch();
        echo "\nTotal users: " . $userCount['count'] . "\n";
        
        // List all users
        $users = $db->query('SELECT id, name, email, role FROM User')->fetchAll();
        foreach ($users as $user) {
            echo "User: " . $user['email'] . " (Role: " . $user['role'] . ")\n";
        }
    }
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
