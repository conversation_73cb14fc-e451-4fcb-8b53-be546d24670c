# 🗄️ Updated SQL Files & Deployment Guide

## 📁 **Available SQL Files**

### **1. Complete Database Setup** ⭐ **RECOMMENDED**
```
php-backend/config/complete-database-setup.sql
```
- **Use for**: Fresh production deployment
- **Contains**: All tables, indexes, and admin user
- **Features**: Complete database structure with optimizations

### **2. Original Database Schema**
```
php-backend/config/database.sql
```
- **Use for**: Reference or manual setup
- **Contains**: Basic table structure and admin user
- **Updated**: Fixed admin user credentials

### **3. Admin Credentials Update**
```
php-backend/config/update-admin-credentials.sql
```
- **Use for**: Updating existing admin user only
- **Contains**: Admin user update/insert with correct password
- **Safe**: Won't affect existing data

## 🔑 **Admin Credentials**

```
Email: <EMAIL>
Password: Admin@2024!
```

**Password Hash (verified working):**
```
$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi
```

## 🚀 **Deployment Instructions**

### **For Fresh Production Setup:**

1. **Create Database**
   ```sql
   -- Through hosting control panel or command line
   CREATE DATABASE u357173570_housingokayy;
   ```

2. **Import Complete Setup**
   ```bash
   mysql -u u357173570_housingokayy -p u357173570_housingokayy < php-backend/config/complete-database-setup.sql
   ```

3. **Update Database Config**
   ```php
   // In php-backend/config/database.php
   private $password = 'your_actual_mysql_password';
   ```

4. **Test Admin Login**
   - URL: `https://yourdomain.com/admin/login`
   - Email: `<EMAIL>`
   - Password: `Admin@2024!`

### **For Existing Database Update:**

1. **Update Admin Only**
   ```bash
   mysql -u username -p database_name < php-backend/config/update-admin-credentials.sql
   ```

2. **Or Run PHP Script**
   ```bash
   php setup-production-admin.php
   ```

## 🛠️ **Setup Scripts**

### **Development Setup:**
```bash
php setup-development-admin.php
```
- Sets up admin in SQLite database
- For local development only
- Automatically detects development environment

### **Production Setup:**
```bash
php setup-production-admin.php
```
- Sets up admin in MySQL database
- Includes verification and testing
- Shows database statistics

## 📊 **Database Structure**

### **Tables Created:**
- ✅ `users` - User accounts and admin
- ✅ `properties` - Property listings
- ✅ `saved_properties` - User saved properties
- ✅ `inquiries` - Property inquiries
- ✅ `contact_messages` - Contact form submissions
- ✅ `blog_posts` - Blog content
- ✅ `user_sessions` - Authentication sessions

### **Indexes for Performance:**
- ✅ Property type, city, status
- ✅ User email and role
- ✅ Session tokens
- ✅ Foreign key relationships

## 🔧 **Troubleshooting**

### **Admin Login Issues:**
1. **Check password hash:**
   ```sql
   SELECT email, password FROM users WHERE role = 'ADMIN';
   ```

2. **Reset admin password:**
   ```sql
   UPDATE users SET password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' 
   WHERE email = '<EMAIL>';
   ```

3. **Verify admin exists:**
   ```sql
   SELECT * FROM users WHERE email = '<EMAIL>';
   ```

### **Database Connection Issues:**
1. Check credentials in `php-backend/config/database.php`
2. Verify database exists
3. Check user permissions
4. Test connection with setup script

### **Missing Tables:**
1. Import `complete-database-setup.sql`
2. Check for SQL errors during import
3. Verify database permissions

## ✅ **Verification Checklist**

After deployment, verify:
- [ ] Database connection works
- [ ] All tables exist
- [ ] Admin user exists with correct role
- [ ] Admin login works at `/admin/login`
- [ ] Password verification works
- [ ] Sessions are created properly

## 🔒 **Security Notes**

1. **Change default password** after first login
2. **Update database credentials** in config file
3. **Remove setup scripts** after deployment
4. **Enable HTTPS** for production
5. **Regular database backups**

---

## 🎯 **Quick Start Commands**

### **Production Deployment:**
```bash
# 1. Import database
mysql -u username -p database_name < php-backend/config/complete-database-setup.sql

# 2. Update config
nano php-backend/config/database.php

# 3. Test setup
php setup-production-admin.php

# 4. Login
# URL: https://yourdomain.com/admin/login
# Email: <EMAIL>
# Password: Admin@2024!
```

### **Development Setup:**
```bash
# 1. Setup Prisma
npx prisma db push

# 2. Create admin
php setup-development-admin.php

# 3. Start servers
npm run dev & php -S localhost:8000

# 4. Login
# URL: http://localhost:3000/admin/login
```

---

**🎉 Your database is now ready with the latest updates and fixes!**
