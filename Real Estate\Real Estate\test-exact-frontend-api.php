<?php
echo "🧪 Testing Exact Frontend API Call...\n\n";

// Test the exact URL that the frontend is calling
$url = 'http://localhost:8000/php-backend/api/auth/login.php';
$data = [
    'email' => '<EMAIL>',
    'password' => 'Admin@2024!'
];

echo "Testing URL: $url\n";
echo "Data: " . json_encode($data) . "\n\n";

$options = [
    'http' => [
        'header' => [
            "Content-type: application/json",
            "Accept: application/json"
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "❌ Failed to connect to API\n";
    echo "Check if PHP server is running on localhost:8000\n";
} else {
    echo "✅ API Response:\n";
    echo $result . "\n\n";
    
    $response = json_decode($result, true);
    if ($response && isset($response['success']) && $response['success']) {
        echo "🎉 Login API working correctly!\n";
        echo "User: " . $response['user']['email'] . "\n";
        echo "Role: " . $response['user']['role'] . "\n";
        echo "Session Token: " . substr($response['session_token'], 0, 20) . "...\n";
    } else {
        echo "❌ Login failed\n";
        if (isset($response['error'])) {
            echo "Error: " . $response['error'] . "\n";
        }
    }
}

// Also test if the server is responding at all
echo "\n🔍 Testing server connectivity...\n";
$testUrl = 'http://localhost:8000/';
$testResult = @file_get_contents($testUrl);
if ($testResult !== FALSE) {
    echo "✅ PHP server is responding on localhost:8000\n";
} else {
    echo "❌ PHP server not responding on localhost:8000\n";
    echo "Make sure to run: php -S localhost:8000\n";
}
?>
