<?php
/**
 * Production Admin Setup Script
 * Creates admin user in MySQL database for production deployment
 * 
 * IMPORTANT: Update database credentials in php-backend/config/database.php first!
 */

echo "🚀 Setting up Production Admin User...\n\n";

// Check if this is being run in production environment
if (php_sapi_name() === 'cli-server' && $_SERVER['HTTP_HOST'] === 'localhost:8000') {
    echo "⚠️  WARNING: This appears to be a development environment\n";
    echo "This script is intended for production MySQL setup\n";
    echo "For development, use: php setup-development-admin.php\n\n";
    
    $confirm = readline("Continue anyway? (y/N): ");
    if (strtolower($confirm) !== 'y') {
        echo "Setup cancelled.\n";
        exit(0);
    }
}

try {
    // Load database configuration
    require_once 'php-backend/config/database.php';
    
    echo "🔌 Connecting to MySQL database...\n";
    $database = new Database();
    $db = $database->getConnection();
    echo "✅ Connected to MySQL database\n";
    
    // Admin credentials
    $adminEmail = '<EMAIL>';
    $adminPassword = 'Admin@2024!';
    $hashedPassword = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'; // Pre-generated hash
    
    echo "🔑 Using verified password hash\n";
    
    // Check if users table exists
    echo "\n📋 Checking database structure...\n";
    $stmt = $db->query("SHOW TABLES LIKE 'users'");
    $table = $stmt->fetch();
    
    if (!$table) {
        echo "❌ Users table doesn't exist\n";
        echo "💡 Please import the database.sql file first:\n";
        echo "   mysql -u username -p database_name < php-backend/config/complete-database-setup.sql\n";
        exit(1);
    }
    echo "✅ Users table exists\n";
    
    // Check if admin exists
    echo "\n👤 Checking for existing admin user...\n";
    $stmt = $db->prepare('SELECT id, name, email, role, is_active FROM users WHERE email = :email');
    $stmt->execute(['email' => $adminEmail]);
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        // Update existing admin
        echo "👤 Updating existing admin user...\n";
        $stmt = $db->prepare('UPDATE users SET password = :password, role = :role, is_active = 1, updated_at = NOW() WHERE email = :email');
        $stmt->execute([
            'password' => $hashedPassword,
            'role' => 'ADMIN',
            'email' => $adminEmail
        ]);
        echo "✅ Admin user updated successfully\n";
    } else {
        // Create new admin
        echo "👤 Creating new admin user...\n";
        $stmt = $db->prepare('INSERT INTO users (id, name, email, password, role, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())');
        $adminId = 'admin_' . uniqid();
        $stmt->execute([
            $adminId,
            'Admin User',
            $adminEmail,
            $hashedPassword,
            'ADMIN',
            1
        ]);
        echo "✅ Admin user created successfully\n";
    }
    
    // Verify admin user
    echo "\n🔍 Verifying admin user...\n";
    $stmt = $db->prepare('SELECT id, name, email, role, is_active, created_at FROM users WHERE email = :email');
    $stmt->execute(['email' => $adminEmail]);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ Admin user verified:\n";
        echo "   ID: " . $admin['id'] . "\n";
        echo "   Name: " . $admin['name'] . "\n";
        echo "   Email: " . $admin['email'] . "\n";
        echo "   Role: " . $admin['role'] . "\n";
        echo "   Active: " . ($admin['is_active'] ? 'Yes' : 'No') . "\n";
        echo "   Created: " . $admin['created_at'] . "\n";
    } else {
        echo "❌ Admin user verification failed\n";
        exit(1);
    }
    
    // Test password verification
    echo "\n🧪 Testing password verification...\n";
    $stmt = $db->prepare('SELECT password FROM users WHERE email = :email');
    $stmt->execute(['email' => $adminEmail]);
    $storedPassword = $stmt->fetchColumn();
    
    if (password_verify($adminPassword, $storedPassword)) {
        echo "✅ Password verification successful\n";
    } else {
        echo "❌ Password verification failed\n";
        exit(1);
    }
    
    // Clean up old sessions
    echo "\n🧹 Cleaning up old sessions...\n";
    $stmt = $db->prepare('DELETE FROM user_sessions WHERE expires_at < NOW()');
    $stmt->execute();
    $deletedSessions = $stmt->rowCount();
    echo "✅ Cleaned up $deletedSessions expired sessions\n";
    
    // Show database statistics
    echo "\n📊 Database Statistics:\n";
    $stats = [
        'Total Users' => $db->query('SELECT COUNT(*) FROM users')->fetchColumn(),
        'Admin Users' => $db->query('SELECT COUNT(*) FROM users WHERE role = "ADMIN"')->fetchColumn(),
        'Active Users' => $db->query('SELECT COUNT(*) FROM users WHERE is_active = 1')->fetchColumn(),
        'Total Properties' => $db->query('SELECT COUNT(*) FROM properties')->fetchColumn(),
        'Active Sessions' => $db->query('SELECT COUNT(*) FROM user_sessions WHERE expires_at > NOW()')->fetchColumn()
    ];
    
    foreach ($stats as $label => $count) {
        echo "   $label: $count\n";
    }
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "🎉 PRODUCTION ADMIN SETUP COMPLETE!\n";
    echo str_repeat("=", 60) . "\n";
    echo "📧 Email: $adminEmail\n";
    echo "🔑 Password: $adminPassword\n";
    echo "🌐 Admin URL: https://yourdomain.com/admin/login\n";
    echo "🔒 IMPORTANT: Change the default password after first login!\n";
    echo str_repeat("=", 60) . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "💡 Database connection failed. Please check:\n";
        echo "   1. Database credentials in php-backend/config/database.php\n";
        echo "   2. Database exists and user has proper permissions\n";
        echo "   3. MySQL server is running\n";
    } elseif (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "💡 Database doesn't exist. Please:\n";
        echo "   1. Create the database through your hosting control panel\n";
        echo "   2. Import the complete-database-setup.sql file\n";
    }
    
    exit(1);
}
?>
