<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get session token
    $session_token = null;
    
    if (isset($_COOKIE['session_token'])) {
        $session_token = $_COOKIE['session_token'];
    } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $auth_header = $_SERVER['HTTP_AUTHORIZATION'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $session_token = $matches[1];
        }
    }
    
    if ($session_token) {
        // Delete session from database
        $query = "DELETE FROM user_sessions WHERE session_token = :token";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':token', $session_token);
        $stmt->execute();
        
        // Clear session cookie
        setcookie('session_token', '', time() - 3600, '/', '', false, true);
    }
    
    sendResponse(['success' => true, 'message' => 'Logged out successfully']);
    
} catch (Exception $e) {
    error_log("Logout error: " . $e->getMessage());
    sendError('Logout failed', 500);
}
?>
