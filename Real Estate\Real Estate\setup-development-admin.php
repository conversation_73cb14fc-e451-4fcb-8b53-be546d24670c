<?php
/**
 * Development Admin Setup Script
 * Creates admin user in SQLite database for local development
 */

echo "🔧 Setting up Development Admin User...\n\n";

try {
    // Connect to SQLite database
    $db = new PDO('sqlite:prisma/dev.db');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to SQLite database\n";
    
    // Admin credentials
    $adminEmail = '<EMAIL>';
    $adminPassword = 'Admin@2024!';
    $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
    
    echo "🔑 Generating password hash...\n";
    echo "Password hash: $hashedPassword\n\n";
    
    // Check if admin exists
    $stmt = $db->prepare('SELECT id, name, email, role FROM User WHERE email = :email');
    $stmt->execute(['email' => $adminEmail]);
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        // Update existing admin
        echo "👤 Updating existing admin user...\n";
        $stmt = $db->prepare('UPDATE User SET password = :password, role = :role, isActive = 1, updatedAt = :updated WHERE email = :email');
        $stmt->execute([
            'password' => $hashedPassword,
            'role' => 'ADMIN',
            'updated' => date('Y-m-d H:i:s'),
            'email' => $adminEmail
        ]);
        echo "✅ Admin user updated successfully\n";
    } else {
        // Create new admin
        echo "👤 Creating new admin user...\n";
        $stmt = $db->prepare('INSERT INTO User (id, name, email, password, role, isActive, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
        $adminId = 'admin_' . uniqid();
        $now = date('Y-m-d H:i:s');
        $stmt->execute([
            $adminId,
            'Admin User',
            $adminEmail,
            $hashedPassword,
            'ADMIN',
            1,
            $now,
            $now
        ]);
        echo "✅ Admin user created successfully\n";
    }
    
    // Verify admin user
    echo "\n🔍 Verifying admin user...\n";
    $stmt = $db->prepare('SELECT id, name, email, role, isActive, createdAt FROM User WHERE email = :email');
    $stmt->execute(['email' => $adminEmail]);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ Admin user verified:\n";
        echo "   ID: " . $admin['id'] . "\n";
        echo "   Name: " . $admin['name'] . "\n";
        echo "   Email: " . $admin['email'] . "\n";
        echo "   Role: " . $admin['role'] . "\n";
        echo "   Active: " . ($admin['isActive'] ? 'Yes' : 'No') . "\n";
        echo "   Created: " . $admin['createdAt'] . "\n";
    } else {
        echo "❌ Admin user verification failed\n";
        exit(1);
    }
    
    // Test password verification
    echo "\n🧪 Testing password verification...\n";
    $stmt = $db->prepare('SELECT password FROM User WHERE email = :email');
    $stmt->execute(['email' => $adminEmail]);
    $storedPassword = $stmt->fetchColumn();
    
    if (password_verify($adminPassword, $storedPassword)) {
        echo "✅ Password verification successful\n";
    } else {
        echo "❌ Password verification failed\n";
        exit(1);
    }
    
    // Clean up old sessions
    echo "\n🧹 Cleaning up old sessions...\n";
    try {
        $stmt = $db->prepare('DELETE FROM UserSession WHERE expiresAt < datetime("now")');
        $stmt->execute();
        $deletedSessions = $stmt->rowCount();
        echo "✅ Cleaned up $deletedSessions expired sessions\n";
    } catch (Exception $e) {
        echo "ℹ️  Session cleanup skipped (table may not exist yet)\n";
    }
    
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "🎉 DEVELOPMENT ADMIN SETUP COMPLETE!\n";
    echo str_repeat("=", 60) . "\n";
    echo "📧 Email: $adminEmail\n";
    echo "🔑 Password: $adminPassword\n";
    echo "🌐 Admin URL: http://localhost:3000/admin/login\n";
    echo "🔧 Make sure both servers are running:\n";
    echo "   - Next.js: npm run dev (port 3000)\n";
    echo "   - PHP API: php -S localhost:8000 (port 8000)\n";
    echo str_repeat("=", 60) . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "💡 Make sure you've run 'npx prisma db push' first\n";
    exit(1);
}
?>
