<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if user is admin
    $currentUser = getCurrentUser($db);
    if (!$currentUser || $currentUser['role'] !== 'ADMIN') {
        sendError('Admin access required', 403);
    }
    
    // Get all users with their property counts
    $query = "SELECT u.*, 
              (SELECT COUNT(*) FROM properties WHERE owner_id = u.id) as property_count,
              (SELECT COUNT(*) FROM properties WHERE owner_id = u.id AND approval_status = 'APPROVED') as approved_properties,
              (SELECT COUNT(*) FROM properties WHERE owner_id = u.id AND approval_status = 'PENDING') as pending_properties
              FROM users u 
              ORDER BY u.created_at DESC";
    
    $stmt = $db->prepare($query);
    $stmt->execute();
    
    $users = $stmt->fetchAll();
    
    // Process users data
    foreach ($users as &$user) {
        // Remove sensitive data
        unset($user['password']);
        
        // Convert boolean fields
        $user['is_active'] = (bool)$user['is_active'];
        
        // Convert numeric fields
        $user['property_count'] = (int)$user['property_count'];
        $user['approved_properties'] = (int)$user['approved_properties'];
        $user['pending_properties'] = (int)$user['pending_properties'];
    }
    
    sendResponse([
        'success' => true,
        'users' => $users,
        'total' => count($users)
    ]);
    
} catch (Exception $e) {
    error_log("Get users error: " . $e->getMessage());
    sendError('Failed to get users', 500);
}

// Helper function to get current user
function getCurrentUser($db) {
    // Check for session token in cookies
    if (!isset($_COOKIE['session_token'])) {
        return null;
    }
    
    $session_token = $_COOKIE['session_token'];
    
    $query = "SELECT u.* FROM users u 
              JOIN user_sessions s ON u.id = s.user_id 
              WHERE s.session_token = :token AND s.expires_at > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();
    
    return $stmt->fetch();
}
?>
