<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if user is admin
    $currentUser = getCurrentUser($db);
    if (!$currentUser || $currentUser['role'] !== 'ADMIN') {
        sendError('Admin access required', 403);
    }
    
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['user_id'])) {
        sendError('Missing user_id', 400);
    }
    
    $userId = $input['user_id'];
    
    // Check if user exists and is not an admin
    $checkQuery = "SELECT role FROM users WHERE id = :user_id";
    $checkStmt = $db->prepare($checkQuery);
    $checkStmt->bindParam(':user_id', $userId);
    $checkStmt->execute();
    
    $user = $checkStmt->fetch();
    if (!$user) {
        sendError('User not found', 404);
    }
    
    if ($user['role'] === 'ADMIN') {
        sendError('Cannot delete admin user', 403);
    }
    
    // Start transaction
    $db->beginTransaction();
    
    try {
        // Delete user's properties first (cascade delete)
        $deletePropertiesQuery = "DELETE FROM properties WHERE owner_id = :user_id";
        $deletePropertiesStmt = $db->prepare($deletePropertiesQuery);
        $deletePropertiesStmt->bindParam(':user_id', $userId);
        $deletePropertiesStmt->execute();
        
        // Delete user's sessions
        $deleteSessionsQuery = "DELETE FROM user_sessions WHERE user_id = :user_id";
        $deleteSessionsStmt = $db->prepare($deleteSessionsQuery);
        $deleteSessionsStmt->bindParam(':user_id', $userId);
        $deleteSessionsStmt->execute();
        
        // Delete user's inquiries
        $deleteInquiriesQuery = "DELETE FROM inquiries WHERE user_id = :user_id";
        $deleteInquiriesStmt = $db->prepare($deleteInquiriesQuery);
        $deleteInquiriesStmt->bindParam(':user_id', $userId);
        $deleteInquiriesStmt->execute();
        
        // Delete the user
        $deleteUserQuery = "DELETE FROM users WHERE id = :user_id";
        $deleteUserStmt = $db->prepare($deleteUserQuery);
        $deleteUserStmt->bindParam(':user_id', $userId);
        $deleteUserStmt->execute();
        
        // Commit transaction
        $db->commit();
        
        sendResponse([
            'success' => true,
            'message' => 'User and all associated data deleted successfully'
        ]);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Delete user error: " . $e->getMessage());
    sendError('Failed to delete user', 500);
}

// Helper function to get current user
function getCurrentUser($db) {
    // Check for session token in cookies
    if (!isset($_COOKIE['session_token'])) {
        return null;
    }
    
    $session_token = $_COOKIE['session_token'];
    
    $query = "SELECT u.* FROM users u 
              JOIN user_sessions s ON u.id = s.user_id 
              WHERE s.session_token = :token AND s.expires_at > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();
    
    return $stmt->fetch();
}
?>
