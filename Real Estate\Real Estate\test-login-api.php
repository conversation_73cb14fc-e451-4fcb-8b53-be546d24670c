<?php
echo "🧪 Testing Login API...\n\n";

// Test the login API endpoint
$url = 'http://localhost:8000/php-backend/api/auth/login.php';
$data = [
    'email' => '<EMAIL>',
    'password' => 'Admin@2024!'
];

$options = [
    'http' => [
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "❌ Failed to connect to login API\n";
    echo "Make sure PHP server is running on localhost:8000\n";
} else {
    echo "✅ API Response:\n";
    echo $result . "\n";
    
    $response = json_decode($result, true);
    if ($response && isset($response['success']) && $response['success']) {
        echo "\n🎉 Login successful!\n";
        echo "User: " . $response['user']['email'] . "\n";
        echo "Role: " . $response['user']['role'] . "\n";
    } else {
        echo "\n❌ Login failed\n";
        if (isset($response['error'])) {
            echo "Error: " . $response['error'] . "\n";
        }
    }
}
?>
