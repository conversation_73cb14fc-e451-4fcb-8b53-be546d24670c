<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check authentication
    $user = getCurrentUser($db);
    if (!$user) {
        sendError('Authentication required', 401);
    }
    
    // Get user's inquiries
    $query = "SELECT i.*, p.title as property_title, p.price as property_price, p.city as property_city
              FROM inquiries i 
              JOIN properties p ON i.property_id = p.id 
              WHERE i.user_id = :user_id 
              ORDER BY i.created_at DESC";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user['id']);
    $stmt->execute();
    
    $inquiries = $stmt->fetchAll();

    // Process inquiries data for frontend compatibility
    foreach ($inquiries as &$inquiry) {
        // Structure the property data as expected by frontend
        $inquiry['property'] = [
            'title' => $inquiry['property_title'],
            'price' => (int)$inquiry['property_price'],
            'currency' => 'INR',
            'city' => $inquiry['property_city']
        ];

        // Clean up the flat fields
        unset($inquiry['property_title']);
        unset($inquiry['property_price']);
        unset($inquiry['property_city']);
    }

    sendResponse([
        'success' => true,
        'inquiries' => $inquiries,
        'total' => count($inquiries)
    ]);
    
} catch (Exception $e) {
    error_log("User inquiries error: " . $e->getMessage());
    sendError('Failed to fetch inquiries', 500);
}

function getCurrentUser($db) {
    // Get session token
    $session_token = null;
    
    if (isset($_COOKIE['session_token'])) {
        $session_token = $_COOKIE['session_token'];
    } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $auth_header = $_SERVER['HTTP_AUTHORIZATION'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $session_token = $matches[1];
        }
    }
    
    if (!$session_token) {
        return null;
    }
    
    // Check session
    $query = "SELECT u.* FROM users u 
              JOIN user_sessions s ON u.id = s.user_id 
              WHERE s.session_token = :token AND s.expires_at > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();
    
    return $stmt->fetch();
}
?>
