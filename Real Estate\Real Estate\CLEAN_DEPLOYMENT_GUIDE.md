# 🧹 Clean Deployment Guide - Single PHP Backend

## ✅ **Cleaned Up Structure**

I've removed the confusing duplicate `php-backend` folder. Now you have a **SINGLE, CLEAN** structure:

```
Real Estate/Real Estate/
├── php-backend/          ← **ONLY PHP BACKEND** (Development & Source)
│   ├── api/             ← All API endpoints
│   └── config/          ← Database config & SQL files
├── out/                 ← **STATIC BUILD** (Frontend only)
│   ├── index.html       ← Homepage
│   ├── admin/           ← Admin pages
│   ├── blog/            ← Blog pages
│   ├── properties/      ← Property pages
│   └── uploads/         ← User uploaded files
├── src/                 ← Next.js source code
├── prisma/              ← SQLite database (development)
└── package.json         ← Project config
```

## 🎯 **How to Use Each Folder**

### **For Development (localhost):**
- **Frontend**: `npm run dev` (serves from `src/`)
- **Backend**: `php -S localhost:8000` (serves `php-backend/`)
- **Database**: SQLite (`prisma/dev.db`)

### **For Production Deployment:**
- **Frontend**: Upload `out/` folder contents to `public_html/`
- **Backend**: Upload `php-backend/` folder to `public_html/php-backend/`
- **Database**: Import SQL files from `php-backend/config/`

## 🚀 **Production Deployment Steps**

### **Step 1: Upload Files**
```
Upload to public_html/:
├── index.html           ← From out/
├── admin/               ← From out/
├── blog/                ← From out/
├── properties/          ← From out/
├── uploads/             ← From out/
├── _next/               ← From out/
└── php-backend/         ← From Real Estate/Real Estate/
    ├── api/
    └── config/
```

### **Step 2: Setup Database**
```bash
# Import SQL files (in order)
mysql -u username -p database_name < php-backend/config/complete-database-setup.sql
mysql -u username -p database_name < php-backend/config/blog-data.sql
mysql -u username -p database_name < php-backend/config/sample-properties.sql
```

### **Step 3: Configure Database**
```php
// Update php-backend/config/database.php
private $password = 'your_actual_mysql_password';
```

## 📁 **SQL Files Location**
All SQL files are in: `php-backend/config/`
- ✅ `complete-database-setup.sql` - Main database setup
- ✅ `blog-data.sql` - Blog content (fixed)
- ✅ `sample-properties.sql` - Sample properties (fixed)

## 🔑 **Admin Login**
- **URL**: `https://yourdomain.com/admin/login`
- **Email**: `<EMAIL>`
- **Password**: `Admin@2024!`

## ✅ **Benefits of Clean Structure**

1. **No Confusion**: Only ONE php-backend folder
2. **Clear Separation**: Development vs Production files
3. **Easy Deployment**: Upload specific folders only
4. **Maintainable**: Source code separate from build output
5. **Fixed SQL**: No more foreign key constraint errors

## 🎉 **Summary**

- **Development**: Work in `Real Estate/Real Estate/` with `php-backend/`
- **Production**: Upload `out/` + `php-backend/` to hosting
- **Database**: Import 3 SQL files from `php-backend/config/`
- **Admin**: Login with provided credentials

**No more confusion - clean, simple, and working!** 🎯
