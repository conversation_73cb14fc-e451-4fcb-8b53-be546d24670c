<!DOCTYPE html>
<html>
<head>
    <title>Test Frontend API</title>
</head>
<body>
    <h1>Test Admin Login API</h1>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:8000/php-backend/api/auth/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Admin@2024!'
                    })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <h3>✅ Login Successful!</h3>
                        <p>User: ${data.user.email}</p>
                        <p>Role: ${data.user.role}</p>
                        <p>Session Token: ${data.session_token.substring(0, 20)}...</p>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>❌ Login Failed</h3>
                        <p>Error: ${data.error || 'Unknown error'}</p>
                    `;
                }
            } catch (error) {
                console.error('Fetch error:', error);
                resultDiv.innerHTML = `
                    <h3>❌ Network Error</h3>
                    <p>Error: ${error.message}</p>
                    <p>Check console for details</p>
                `;
            }
        }
    </script>
</body>
</html>
