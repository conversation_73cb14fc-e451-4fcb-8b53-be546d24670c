<?php
echo "<h1>🔧 Production Debug - Housing.okayy.in</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style>";

echo "<h2>1. Testing Database Connection</h2>";

try {
    // Test database connection
    $db = new PDO('mysql:host=localhost;dbname=u357173570_housingokayy', 'u357173570_housingokayy', 'your_secure_password');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Database connection successful</p>";
    
    echo "<h2>2. Checking Database Tables</h2>";
    
    // Check if users table exists
    $tables = $db->query("SHOW TABLES LIKE 'users'")->fetchAll();
    if (empty($tables)) {
        echo "<p class='error'>❌ 'users' table does not exist</p>";
        echo "<p class='info'>📋 You need to import: complete-database-setup.sql</p>";
    } else {
        echo "<p class='success'>✅ 'users' table exists</p>";
        
        echo "<h2>3. Checking Admin User</h2>";
        
        // Check for admin user
        $stmt = $db->prepare("SELECT id, name, email, role, is_active, created_at FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$admin) {
            echo "<p class='error'>❌ Admin user does not exist</p>";
            echo "<p class='info'>📋 You need to import: blog-data.sql or sample-properties.sql</p>";
            
            echo "<h3>🔧 Quick Fix - Create Admin User:</h3>";
            echo "<pre>INSERT INTO users (id, name, email, password, role, is_active, created_at, updated_at) VALUES
('admin-housing-okayy', 'Admin User', '<EMAIL>', '\$2y\$10\$eayb4pcLzPuJcexSBE14Suz7jMLa8z4N2lcJwGroH1fmFHlYg0U9K', 'ADMIN', TRUE, NOW(), NOW());</pre>";
            
        } else {
            echo "<p class='success'>✅ Admin user exists</p>";
            echo "<pre>";
            print_r($admin);
            echo "</pre>";
            
            echo "<h2>4. Testing Password Verification</h2>";
            
            // Get the stored password hash
            $stmt = $db->prepare("SELECT password FROM users WHERE email = ?");
            $stmt->execute(['<EMAIL>']);
            $storedHash = $stmt->fetchColumn();
            
            $testPassword = 'Admin@2024!';
            $isValid = password_verify($testPassword, $storedHash);
            
            if ($isValid) {
                echo "<p class='success'>✅ Password verification successful</p>";
                echo "<p class='info'>Password 'Admin@2024!' works correctly</p>";
            } else {
                echo "<p class='error'>❌ Password verification failed</p>";
                echo "<p class='info'>Stored hash: " . htmlspecialchars($storedHash) . "</p>";
                echo "<p class='info'>Test password: " . htmlspecialchars($testPassword) . "</p>";
                
                // Generate new hash
                $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
                echo "<h3>🔧 Fix Password Hash:</h3>";
                echo "<pre>UPDATE users SET password = '$newHash' WHERE email = '<EMAIL>';</pre>";
            }
            
            echo "<h2>5. Testing Login API</h2>";
            
            // Test the actual login process
            $loginData = json_encode(['email' => '<EMAIL>', 'password' => 'Admin@2024!']);
            
            echo "<p class='info'>Testing API endpoint: /php-backend/api/auth/login.php</p>";
            echo "<p class='info'>Request data: " . htmlspecialchars($loginData) . "</p>";
            
            // Simulate the login process
            if ($isValid) {
                echo "<p class='success'>✅ Login API should work</p>";
                echo "<p class='info'>Admin login should work at: https://housing.okayy.in/admin/login</p>";
            } else {
                echo "<p class='error'>❌ Login API will fail due to password issue</p>";
            }
        }
        
        echo "<h2>6. Database Statistics</h2>";
        
        // Show table counts
        $userCount = $db->query("SELECT COUNT(*) FROM users")->fetchColumn();
        echo "<p class='info'>Total users: $userCount</p>";
        
        // Check if other tables exist
        $tables = ['properties', 'blog_posts', 'sessions'];
        foreach ($tables as $table) {
            $exists = $db->query("SHOW TABLES LIKE '$table'")->fetchAll();
            if (!empty($exists)) {
                $count = $db->query("SELECT COUNT(*) FROM $table")->fetchColumn();
                echo "<p class='info'>$table: $count records</p>";
            } else {
                echo "<p class='error'>❌ Table '$table' missing</p>";
            }
        }
    }
    
} catch (PDOException $e) {
    echo "<p class='error'>❌ Database connection failed</p>";
    echo "<p class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "<h3>🔧 Database Password Issue:</h3>";
        echo "<p class='info'>Update the password in: php-backend/config/database.php</p>";
        echo "<pre>private \$password = 'your_actual_mysql_password';</pre>";
    }
    
    if (strpos($e->getMessage(), 'Unknown database') !== false) {
        echo "<h3>🔧 Database Not Found:</h3>";
        echo "<p class='info'>Make sure database 'u357173570_housingokayy' exists in your hosting panel</p>";
    }
}

echo "<h2>7. Next Steps</h2>";
echo "<ol>";
echo "<li>If database connection failed: Update password in php-backend/config/database.php</li>";
echo "<li>If tables missing: Import complete-database-setup.sql in phpMyAdmin</li>";
echo "<li>If admin user missing: Import blog-data.sql or run the SQL command above</li>";
echo "<li>If password verification failed: Run the UPDATE command above</li>";
echo "</ol>";

echo "<p class='info'>📧 After fixing, test login at: <a href='https://housing.okayy.in/admin/login'>https://housing.okayy.in/admin/login</a></p>";
echo "<p class='info'>🔑 Credentials: <EMAIL> / Admin@2024!</p>";
?>
