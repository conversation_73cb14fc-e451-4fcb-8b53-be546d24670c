<?php
require_once '../../config/database.php';

setCorsHeaders();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('Method not allowed', 405);
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if user is admin
    $currentUser = getCurrentUser($db);
    if (!$currentUser || $currentUser['role'] !== 'ADMIN') {
        sendError('Admin access required', 403);
    }
    
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['property_id']) || !isset($input['reason'])) {
        sendError('Missing property_id or reason', 400);
    }
    
    $propertyId = $input['property_id'];
    $reason = $input['reason'];
    
    // Check if property exists
    $checkQuery = "SELECT id, approval_status FROM properties WHERE id = :property_id";
    $checkStmt = $db->prepare($checkQuery);
    $checkStmt->bindParam(':property_id', $propertyId);
    $checkStmt->execute();
    
    $property = $checkStmt->fetch();
    if (!$property) {
        sendError('Property not found', 404);
    }
    
    // Update property status to rejected
    $query = "UPDATE properties SET 
              approval_status = 'REJECTED',
              is_approved = 0,
              rejection_reason = :reason,
              rejected_at = NOW(),
              rejected_by = :admin_id
              WHERE id = :property_id";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':property_id', $propertyId);
    $stmt->bindParam(':reason', $reason);
    $stmt->bindParam(':admin_id', $currentUser['id']);
    
    if ($stmt->execute()) {
        sendResponse([
            'success' => true,
            'message' => 'Property rejected successfully'
        ]);
    } else {
        sendError('Failed to reject property', 500);
    }
    
} catch (Exception $e) {
    error_log("Reject property error: " . $e->getMessage());
    sendError('Failed to reject property', 500);
}

// Helper function to get current user
function getCurrentUser($db) {
    // Check for session token in cookies
    if (!isset($_COOKIE['session_token'])) {
        return null;
    }
    
    $session_token = $_COOKIE['session_token'];
    
    $query = "SELECT u.* FROM users u 
              JOIN user_sessions s ON u.id = s.user_id 
              WHERE s.session_token = :token AND s.expires_at > NOW()";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':token', $session_token);
    $stmt->execute();
    
    return $stmt->fetch();
}
?>
